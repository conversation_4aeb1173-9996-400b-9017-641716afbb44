package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 乘客推乘客邀请记录详情查询业务对象
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MktPassengerInviteRecordDetailQueryBo extends BaseEntity {

    /**
     * 活动ID
     */
    private Long campaignId;

    /**
     * 邀请人手机号
     */
    private String inviterMobile;

    /**
     * 被邀请人手机号
     */
    private String inviteeMobile;

    /**
     * 邀请状态(1已注册,2已首单)
     */
    private String inviteStatus;

    /**
     * 奖励状态(1已发放,2发放失败,3客诉退券,4客诉退券失败)
     */
    private String rewardStatus;

    /**
     * 优惠券状态(0待使用,1已使用,2已过期,3已作废)
     */
    private String couponStatus;

}
