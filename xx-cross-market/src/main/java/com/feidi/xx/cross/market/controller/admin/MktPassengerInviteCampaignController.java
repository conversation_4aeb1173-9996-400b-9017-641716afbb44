package com.feidi.xx.cross.market.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.idempotent.annotation.RepeatSubmit;
import com.feidi.xx.common.log.annotation.Log;
import com.feidi.xx.common.log.enums.BusinessType;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCampaignBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCampaignService;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 后台 - 乘客推乘客活动主
 * 前端访问路由地址为:/market/passengerInviteCampaign
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/passengerInviteCampaign")
public class MktPassengerInviteCampaignController extends BaseController {

    private final IMktPassengerInviteCampaignService mktPassengerInviteCampaignService;

    /**
     * 查询乘客推乘客活动主列表
     */
    @SaCheckPermission("market:passengerInviteCampaign:list")
    @GetMapping("/list")
    @Enum2TextAspect
    public TableDataInfo<MktPassengerInviteCampaignVo> list(MktPassengerInviteCampaignBo bo, PageQuery pageQuery) {
        return mktPassengerInviteCampaignService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取乘客推乘客活动主详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("market:passengerInviteCampaign:query")
    @GetMapping("/{id}")
    @Enum2TextAspect
    public R<MktPassengerInviteCampaignVo> getInfo(@NotNull(message = "主键不能为空")
                                                   @PathVariable Long id) {
        return R.ok(mktPassengerInviteCampaignService.queryById(id));
    }

    /**
     * 新增乘客推乘客活动主
     */
    @SaCheckPermission("market:passengerInviteCampaign:add")
    @Log(title = "乘客推乘客活动主", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody MktPassengerInviteCampaignBo bo) {
        bo.valid();
        return toAjax(mktPassengerInviteCampaignService.insertByBo(bo));
    }

    /**
     * 修改乘客推乘客活动主
     */
    @SaCheckPermission("market:passengerInviteCampaign:edit")
    @Log(title = "乘客推乘客活动主", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody MktPassengerInviteCampaignBo bo) {
        bo.valid();
        return toAjax(mktPassengerInviteCampaignService.updateByBo(bo));
    }

    /**
     * 删除乘客推乘客活动主
     *
     * @param ids 主键串
     */
    @SaCheckPermission("market:passengerInviteCampaign:remove")
    @Log(title = "乘客推乘客活动主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(mktPassengerInviteCampaignService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 更新乘客推乘客活动状态
     *
     * @param bo 包含id和status的业务对象
     * @return 操作结果
     */
    @SaCheckPermission("market:passengerInviteCampaign:updateStatus")
    @Log(title = "乘客推乘客活动状态更新", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/updateStatus")
    public R<Void> updateStatus(@RequestBody MktPassengerInviteCampaignBo bo) {
        Assert.notNull(bo.getId(), "活动ID不能为空");
        Assert.notNull(bo.getStatus(), "活动状态不能为空");

        // 验证状态值是否有效
        PassengerInviteCampaignStatusEnum.getByCodeThrow(bo.getStatus());

        mktPassengerInviteCampaignService.updateStatus(bo);
        return R.ok();
    }
}
