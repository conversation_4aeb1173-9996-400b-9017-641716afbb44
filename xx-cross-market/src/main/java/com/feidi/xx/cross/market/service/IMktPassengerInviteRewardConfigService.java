package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardConfigBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;

import java.util.List;

/**
 * 乘客推乘客活动奖励配置Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IMktPassengerInviteRewardConfigService {

    /**
     * 查询乘客推乘客活动奖励配置
     *
     * @param id 主键
     * @return 乘客推乘客活动奖励配置
     */
    MktPassengerInviteRewardConfigVo queryById(Long id);

    /**
     * 分页查询乘客推乘客活动奖励配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客活动奖励配置分页列表
     */
    TableDataInfo<MktPassengerInviteRewardConfigVo> queryPageList(MktPassengerInviteRewardConfigBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客推乘客活动奖励配置列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客活动奖励配置列表
     */
    List<MktPassengerInviteRewardConfigVo> queryList(MktPassengerInviteRewardConfigBo bo);

    /**
     * 根据活动id查询奖励配置列表
     *
     * @param campaignId
     * @return
     */
    List<MktPassengerInviteRewardConfigVo> queryListByCampaignId(Long campaignId);
}
