package com.feidi.xx.cross.market.domain.bo;

import com.feidi.xx.common.core.validate.AddGroup;
import com.feidi.xx.common.core.validate.EditGroup;
import com.feidi.xx.common.mybatis.core.domain.BaseEntity;
import com.feidi.xx.cross.common.annotations.EnumValue;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRewardConfig;
import com.feidi.xx.cross.market.domain.common.RewardMeta;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 乘客推乘客活动奖励配置业务对象 mkt_passenger_invite_reward_config
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = MktPassengerInviteRewardConfig.class, reverseConvertGenerate = false)
public class MktPassengerInviteRewardConfigBo extends BaseEntity {

    /**
     * 奖励配置ID
     */
    @NotNull(message = "奖励配置ID不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 活动ID
     */
    private Long campaignId;

    /**
     * 角色类型(1邀请人,2被邀请人)
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum
     */
    @NotNull(message = "角色类型(1邀请人,2被邀请人)不能为空", groups = {AddGroup.class, EditGroup.class})
    @EnumValue(message = "角色类型不正确", value = PassengerInviteRoleTypeEnum.class, compareField = "code")
    private String roleType;

    /**
     * 奖励类型：1=现金 2=优惠券 3=积分
     */
    @NotNull(message = "奖励类型：1=现金 2=优惠券 3=积分不能为空", groups = {AddGroup.class, EditGroup.class})
    @EnumValue(message = "奖励类型不正确", value = PassengerInviteRewardTypeEnum.class, compareField = "code")
    private String rewardType = PassengerInviteRewardTypeEnum.COUPON.getCode();

    /**
     * 奖励金额/积分值(现金或积分用)
     */
    private BigDecimal rewardValue;

    /**
     * 扩展字段(如coupon_ids:["1001","1002"])
     */
    @NotNull(message = "奖励内容不能为空", groups = {AddGroup.class, EditGroup.class})
    @Valid
    private RewardMeta rewardMeta;

    /**
     * 发放条件(1注册成功,2完成首单)
     */
    @NotNull(message = "发放条件(1注册成功,2完成首单)不能为空", groups = {AddGroup.class, EditGroup.class})
    @EnumValue(message = "发放条件不正确", value = PassengerInviteConditionTypeEnum.class, compareField = "code")
    private String conditionType;


}
