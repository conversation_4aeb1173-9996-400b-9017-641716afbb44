package com.feidi.xx.cross.market.controller.admin;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.feidi.xx.common.core.domain.R;
import com.feidi.xx.common.enum2text.annotation.Enum2TextAspect;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordDetailQueryBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 后台 - 乘客推乘客邀请记录
 * 前端访问路由地址为:/market/passengerInviteRecord
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/passengerInviteRecord")
public class MktPassengerInviteRecordController extends BaseController {

    private final IMktPassengerInviteRecordService mktPassengerInviteRecordService;

    /**
     * 查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     */
    @SaCheckPermission("market:passengerInviteRecord:detailList")
    @GetMapping("/detailList")
    @Enum2TextAspect
    public TableDataInfo<MktPassengerInviteRecordDetailVo> detailList(MktPassengerInviteRecordDetailQueryBo bo, PageQuery pageQuery) {
        return mktPassengerInviteRecordService.queryInviteRecordDetailPageList(bo, pageQuery);
    }

    /**
     * 导出乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     */
    @SaCheckPermission("market:passengerInviteRecord:export")
    @GetMapping("/export")
    @Enum2TextAspect
    public R<List<MktPassengerInviteRecordDetailVo>> export(MktPassengerInviteRecordDetailQueryBo bo) {
        List<MktPassengerInviteRecordDetailVo> list = mktPassengerInviteRecordService.queryInviteRecordDetailList(bo);
        return R.ok(list);
    }

}
