package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardVo;

import java.util.Collection;
import java.util.List;

/**
 * 乘客推乘客奖励发放记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IMktPassengerInviteRewardService {

    /**
     * 查询乘客推乘客奖励发放记录
     *
     * @param id 主键
     * @return 乘客推乘客奖励发放记录
     */
    MktPassengerInviteRewardVo queryById(Long id);

    /**
     * 分页查询乘客推乘客奖励发放记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客奖励发放记录分页列表
     */
    TableDataInfo<MktPassengerInviteRewardVo> queryPageList(MktPassengerInviteRewardBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客推乘客奖励发放记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客奖励发放记录列表
     */
    List<MktPassengerInviteRewardVo> queryList(MktPassengerInviteRewardBo bo);

    /**
     * 新增乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否新增成功
     */
    Boolean insertByBo(MktPassengerInviteRewardBo bo);

    /**
     * 修改乘客推乘客奖励发放记录
     *
     * @param bo 乘客推乘客奖励发放记录
     * @return 是否修改成功
     */
    Boolean updateByBo(MktPassengerInviteRewardBo bo);

    /**
     * 校验并批量删除乘客推乘客奖励发放记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
