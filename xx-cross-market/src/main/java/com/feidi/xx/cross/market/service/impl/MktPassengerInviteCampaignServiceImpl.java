package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.exception.ServiceException;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StreamUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.common.satoken.utils.LoginHelper;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.common.utils.MySqlJsonUtil;
import com.feidi.xx.cross.common.utils.NameSetterUtil;
import com.feidi.xx.cross.market.domain.MktCoupon;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCampaign;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRewardConfig;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteCampaignBo;
import com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteCampaignVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.mapper.MktCouponMapper;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteCampaignMapper;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardConfigMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCampaignService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 乘客推乘客活动主Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MktPassengerInviteCampaignServiceImpl implements IMktPassengerInviteCampaignService {

    private final MktPassengerInviteCampaignMapper baseMapper;
    private final MktCouponMapper mktCouponMapper;
    private final MktPassengerInviteRewardConfigMapper mktPassengerInviteRewardConfigMapper;
    private final IMktPassengerInviteRewardConfigService mktPassengerInviteRewardConfigService;
    private final IMktPassengerInviteRecordService mktPassengerInviteRecordService;

    /**
     * 查询乘客推乘客活动主
     *
     * @param id 主键
     * @return 乘客推乘客活动主
     */
    @Override
    public MktPassengerInviteCampaignVo queryById(Long id) {
        MktPassengerInviteCampaignVo mktPassengerInviteCampaignVo = baseMapper.selectVoById(id);
        if (mktPassengerInviteCampaignVo == null) {
            return null;
        }
        List<MktPassengerInviteRewardConfigVo> rewardConfigVos = mktPassengerInviteRewardConfigService.queryListByCampaignId(id);
        mktPassengerInviteCampaignVo.setRewardConfigList(rewardConfigVos);
        return mktPassengerInviteCampaignVo;
    }

    /**
     * 分页查询乘客推乘客活动主列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客活动主分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteCampaignVo> queryPageList(MktPassengerInviteCampaignBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteCampaign> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteCampaignVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        NameSetterUtil.cityNameSetterByCode(result.getRecords(), MktPassengerInviteCampaignVo::getCityCode, MktPassengerInviteCampaignVo::setCityNames);
        Set<Long> ids = StreamUtils.toSet(result.getRecords(), MktPassengerInviteCampaignVo::getId);
        if (CollUtil.isNotEmpty(ids)) {
            Map<Long, InviteStatisticsVo> longInviteStatisticsVoMap = mktPassengerInviteRecordService.queryInviteStatisticsByCampaignIds(ids);
            result.getRecords().forEach(item -> item.setInviteStatistics(longInviteStatisticsVoMap.get(item.getId())));
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客活动主列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客活动主列表
     */
    @Override
    public List<MktPassengerInviteCampaignVo> queryList(MktPassengerInviteCampaignBo bo) {
        LambdaQueryWrapper<MktPassengerInviteCampaign> lqw = buildQueryWrapper(bo);
        List<MktPassengerInviteCampaignVo> list = baseMapper.selectVoList(lqw);
        NameSetterUtil.cityNameSetterByCode(list, MktPassengerInviteCampaignVo::getCityCode, MktPassengerInviteCampaignVo::setCityNames);
        return list;
    }

    private LambdaQueryWrapper<MktPassengerInviteCampaign> buildQueryWrapper(MktPassengerInviteCampaignBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteCampaign> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getActivityName()), MktPassengerInviteCampaign::getActivityName, bo.getActivityName());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteCampaign::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getShareTitle()), MktPassengerInviteCampaign::getShareTitle, bo.getShareTitle());
        MySqlJsonUtil.jsonOverlaps(lqw, "city_code", bo.getCityCode());
        //时间返回交集
        lqw.and(wrapper ->
                wrapper.lt(MktPassengerInviteCampaign::getStartTime, bo.getEndTime()).gt(MktPassengerInviteCampaign::getEndTime, bo.getStartTime()));
        lqw.func(bo.getTimeRangeForCreateTime() != null, wrapper ->
                wrapper.between(MktPassengerInviteCampaign::getCreateTime, bo.getTimeRangeForCreateTime().getStartTime(), bo.getTimeRangeForCreateTime().getEndTime())
        );
        return lqw;
    }

    /**
     * 新增乘客推乘客活动主
     *
     * @param bo 乘客推乘客活动主
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(MktPassengerInviteCampaignBo bo) {
        log.debug("insertByBo: {}", bo);
        validEntityBeforeSave(bo);
        MktPassengerInviteCampaign add = MapstructUtils.convert(bo, MktPassengerInviteCampaign.class);
        boolean flag = baseMapper.insert(add) > 0;
        Assert.isTrue(flag, "新增乘客推乘客活动失败");
        bo.setId(add.getId());
        // 新增奖励配置
        if (CollUtil.isNotEmpty(bo.getRewardConfigList())) {
            bo.getRewardConfigList().forEach(rewardConfig -> rewardConfig.setCampaignId(bo.getId()));
            boolean b = mktPassengerInviteRewardConfigMapper.insertBatch(MapstructUtils.convert(bo.getRewardConfigList(), MktPassengerInviteRewardConfig.class));
            Assert.isTrue(b, "新增奖励配置失败");
        }
        return true;
    }

    /**
     * 修改乘客推乘客活动主
     *
     * @param bo 乘客推乘客活动主
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(MktPassengerInviteCampaignBo bo) {
        log.debug("updateByBo: {}", bo);
        validEntityBeforeSave(bo);
        MktPassengerInviteCampaign update = MapstructUtils.convert(bo, MktPassengerInviteCampaign.class);
        boolean b = baseMapper.updateById(update) > 0;
        Assert.isTrue(b, "修改乘客推乘客活动失败");
        // 删除旧的奖励配置
        mktPassengerInviteRewardConfigMapper.delete(new LambdaQueryWrapper<MktPassengerInviteRewardConfig>().eq(MktPassengerInviteRewardConfig::getCampaignId, bo.getId()));
        // 新增奖励配置
        if (CollUtil.isNotEmpty(bo.getRewardConfigList())) {
            bo.getRewardConfigList().forEach(rewardConfig -> rewardConfig.setCampaignId(bo.getId()));
            b = mktPassengerInviteRewardConfigMapper.insertBatch(MapstructUtils.convert(bo.getRewardConfigList(), MktPassengerInviteRewardConfig.class));
            Assert.isTrue(b, "新增奖励配置失败");
        }
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteCampaignBo bo) {
        //添加的优惠券可使用时间为时间段的，保存活动时需要校验优惠券最晚使用时间必须大于等于活动结束时间。
        // 报错提示：”优惠券名称+最晚可使用时间不能小于活动结束时间”
        Set<Long> couponIds = bo.getRewardConfigList().stream()
                .filter(e -> PassengerInviteRewardTypeEnum.COUPON.getCode().equals(e.getRewardType()))
                .map(e -> e.getRewardMeta().getCouponIds())
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        couponIds.parallelStream().forEach(couponId -> {
            MktCoupon coupon = mktCouponMapper.selectById(couponId);
            if (coupon == null) {
                log.error("优惠券不存在，优惠券ID：{}", couponId);
                throw new ServiceException("优惠券不存在");
            }
            if (coupon.getEndTime().before(bo.getEndTime())) {
                throw new ServiceException(coupon.getName() + "最晚可使用时间不能小于活动结束时间");
            }
        });
        //城市+活动时间校验：同一城市内，不允许存在进行中或待开始活动与新建活动的时间区间重叠。
        var w = new LambdaQueryWrapper<MktPassengerInviteCampaign>()
                .ne(bo.getId() != null, MktPassengerInviteCampaign::getId, bo.getId())
                .in(MktPassengerInviteCampaign::getStatus, Arrays.asList(PassengerInviteCampaignStatusEnum.ONGOING.getCode(), PassengerInviteCampaignStatusEnum.TO_BE_STARTED.getCode()))
                // 校验活动时间是否重叠
                .and(wrapper ->
                        wrapper.lt(MktPassengerInviteCampaign::getStartTime, bo.getEndTime()).gt(MktPassengerInviteCampaign::getEndTime, bo.getStartTime()));
        // 校验城市是否重叠
        MySqlJsonUtil.jsonOverlaps(w, "city_code", bo.getCityCode());
        //XXX城市与已创建的活动时间有冲突，请检查后重新创建
        List<MktPassengerInviteCampaign> list = baseMapper.selectList(w);
        if (CollUtil.isNotEmpty(list)) {
            Set<String> cityCodes = list.stream().map(MktPassengerInviteCampaign::getCityCode).flatMap(Collection::stream).collect(Collectors.toSet());
            Map<String, String> cityNameMap = NameSetterUtil.getCityNameMap(cityCodes);
            throw new ServiceException(String.join("、", cityNameMap.values()) + "城市与已创建的活动时间有冲突，请检查后重新创建");
        }
    }

    /**
     * 校验并批量删除乘客推乘客活动主信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 更新乘客推乘客活动状态
     * 状态说明:
     * 0:待开始 1:进行中 2:已结束
     *
     * @param bo 包含id和status的业务对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(MktPassengerInviteCampaignBo bo) {
        log.debug("updateStatus: {}", bo);
        // 查询活动是否存在
        MktPassengerInviteCampaign campaign = baseMapper.selectById(bo.getId());
        if (campaign == null) {
            throw new ServiceException("乘客推乘客活动不存在");
        }
        // 状态变更业务逻辑校验
        String currentStatus = campaign.getStatus();
        validateStatusTransition(currentStatus, bo.getStatus());

        // 更新状态
        LambdaUpdateWrapper<MktPassengerInviteCampaign> updateWrapper = Wrappers.<MktPassengerInviteCampaign>lambdaUpdate()
                .eq(MktPassengerInviteCampaign::getId, bo.getId())
                .set(MktPassengerInviteCampaign::getStatus, bo.getStatus())
                .set(MktPassengerInviteCampaign::getUpdateTime, new Date())
                .set(MktPassengerInviteCampaign::getUpdateBy, LoginHelper.getUserId());

        boolean updated = baseMapper.update(updateWrapper) > 0;
        if (!updated) {
            throw new ServiceException("更新活动状态失败");
        }

        log.info("活动状态更新成功，活动ID: {}, 原状态: {}, 新状态: {}, 操作人: {}",
                bo.getId(), currentStatus, bo.getStatus(), LoginHelper.getUserId());
    }

    /**
     * 验证状态转换是否合法
     *
     * @param currentStatus 当前状态
     * @param newStatus     新状态
     */
    private void validateStatusTransition(String currentStatus, String newStatus) {
        // 如果状态没有变化，直接返回
        if (currentStatus.equals(newStatus)) {
            return;
        }
        PassengerInviteCampaignStatusEnum current = PassengerInviteCampaignStatusEnum.getByCodeThrow(currentStatus);
        PassengerInviteCampaignStatusEnum target = PassengerInviteCampaignStatusEnum.getByCodeThrow(newStatus);
        // 根据业务规则验证状态转换
        switch (current) {
            case TO_BE_STARTED -> {
                // 待开始状态可以转换为进行中或已结束
                if (target != PassengerInviteCampaignStatusEnum.ONGOING &&
                        target != PassengerInviteCampaignStatusEnum.ENDED) {
                    throw new ServiceException("待开始状态只能转换为进行中或已结束");
                }
            }
            case ONGOING -> {
                // 进行中状态只能转换为已结束
                if (target != PassengerInviteCampaignStatusEnum.ENDED) {
                    throw new ServiceException("进行中状态只能转换为已结束");
                }
            }
            case ENDED -> {
                // 已结束状态不能转换为其他状态
                throw new ServiceException("已结束的活动不能修改状态");
            }
        }
    }
}
