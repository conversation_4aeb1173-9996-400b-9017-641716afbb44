package com.feidi.xx.cross.market.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRewardConfig;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRewardConfigBo;
import com.feidi.xx.cross.market.domain.vo.MktCouponVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRewardConfigVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRewardConfigMapper;
import com.feidi.xx.cross.market.service.IMktCouponService;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRewardConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 乘客推乘客活动奖励配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRewardConfigServiceImpl implements IMktPassengerInviteRewardConfigService {

    private final MktPassengerInviteRewardConfigMapper baseMapper;
    private final IMktCouponService mktCouponService;

    /**
     * 查询乘客推乘客活动奖励配置
     *
     * @param id 主键
     * @return 乘客推乘客活动奖励配置
     */
    @Override
    public MktPassengerInviteRewardConfigVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客活动奖励配置列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客活动奖励配置分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRewardConfigVo> queryPageList(MktPassengerInviteRewardConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteRewardConfig> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRewardConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客活动奖励配置列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客活动奖励配置列表
     */
    @Override
    public List<MktPassengerInviteRewardConfigVo> queryList(MktPassengerInviteRewardConfigBo bo) {
        LambdaQueryWrapper<MktPassengerInviteRewardConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteRewardConfig> buildQueryWrapper(MktPassengerInviteRewardConfigBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteRewardConfig> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteRewardConfig::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getRoleType() != null, MktPassengerInviteRewardConfig::getRoleType, bo.getRoleType());
        lqw.eq(bo.getRewardType() != null, MktPassengerInviteRewardConfig::getRewardType, bo.getRewardType());
        lqw.eq(bo.getRewardValue() != null, MktPassengerInviteRewardConfig::getRewardValue, bo.getRewardValue());
        lqw.eq(bo.getConditionType() != null, MktPassengerInviteRewardConfig::getConditionType, bo.getConditionType());
        return lqw;
    }

    @Override
    public List<MktPassengerInviteRewardConfigVo> queryListByCampaignId(Long campaignId) {
        var rewardConfigVos = baseMapper.selectVoList(Wrappers.<MktPassengerInviteRewardConfig>lambdaQuery()
                .eq(MktPassengerInviteRewardConfig::getCampaignId, campaignId));
        if (CollUtil.isEmpty(rewardConfigVos)) {
            return List.of();
        }

        //查询优惠券信息
        Set<Long> couponIds = rewardConfigVos.stream()
                .filter(e -> Objects.equals(e.getRewardType(), PassengerInviteRewardTypeEnum.COUPON.getCode()))
                .map(e -> e.getRewardMeta().getCouponIds())
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        Map<Long, MktCouponVo> couponMap = mktCouponService.queryMapByIds(couponIds);
        rewardConfigVos.forEach(e -> {
            if (Objects.equals(e.getRewardType(), PassengerInviteRewardTypeEnum.COUPON.getCode())) {
                List<MktCouponVo> mktCouponVos = e.getRewardMeta().getCouponIds().stream()
                        .map(couponMap::get)
                        .filter(Objects::nonNull).collect(Collectors.toList());
                e.setCouponVos(mktCouponVos);
            }
        });
        return rewardConfigVos;
    }
}
