package com.feidi.xx.cross.market.service;

import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordDetailQueryBo;
import com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客邀请记录Service接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface IMktPassengerInviteRecordService {

    /**
     * 查询乘客推乘客邀请记录
     *
     * @param id 主键
     * @return 乘客推乘客邀请记录
     */
    MktPassengerInviteRecordVo queryById(Long id);

    /**
     * 分页查询乘客推乘客邀请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客邀请记录分页列表
     */
    TableDataInfo<MktPassengerInviteRecordVo> queryPageList(MktPassengerInviteRecordBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的乘客推乘客邀请记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客邀请记录列表
     */
    List<MktPassengerInviteRecordVo> queryList(MktPassengerInviteRecordBo bo);

    /**
     * 根据活动id查询邀请统计
     *
     * @param campaignIds 活动id集合
     * @return 邀请统计列表
     */
    Map<Long, InviteStatisticsVo> queryInviteStatisticsByCampaignIds(Collection<Long> campaignIds);

    /**
     * 分页查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邀请记录详情分页列表
     */
    TableDataInfo<MktPassengerInviteRecordDetailVo> queryInviteRecordDetailPageList(MktPassengerInviteRecordDetailQueryBo bo, PageQuery pageQuery);

    /**
     * 查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param bo 查询条件
     * @return 邀请记录详情列表
     */
    List<MktPassengerInviteRecordDetailVo> queryInviteRecordDetailList(MktPassengerInviteRecordDetailQueryBo bo);
}
