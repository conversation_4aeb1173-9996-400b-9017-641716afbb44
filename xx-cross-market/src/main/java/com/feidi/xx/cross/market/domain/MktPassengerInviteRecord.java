package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;

/**
 * 乘客推乘客邀请记录对象 mkt_passenger_invite_record
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("mkt_passenger_invite_record")
public class MktPassengerInviteRecord extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 活动ID
     */
    private Long campaignId;

    /**
     * 邀请人乘客ID
     */
    private Long inviterId;

    /**
     * 邀请人手机号
     */
    private String inviterMobile;

    /**
     * 邀请人城市
     */
    private String inviterCityCode;

    /**
     * 被邀请人乘客ID
     */
    private Long inviteeId;

    /**
     * 被邀请人手机号
     */
    private String inviteeMobile;

    /**
     * 被邀请人城市
     */
    private String inviteeCityCode;

    /**
     * 邀请时间
     */
    private Date inviteTime;

    /**
     * 首单完成时间
     */
    private Date orderCompleteTime;

    /**
     * 首单id
     */
    private Long orderId;

    /**
     * 邀请状态(1已注册,2已首单)
     *
     * @see PassengerInviteStatusEnum
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
