package com.feidi.xx.cross.market.controller.member;

import com.feidi.xx.common.core.constant.WebConstants;
import com.feidi.xx.common.web.core.BaseController;
import com.feidi.xx.cross.market.service.IMktPassengerInviteCampaignService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 后台 - 乘客推乘客活动主
 * 前端访问路由地址为:/market/br/passengerInviteCampaign
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping(WebConstants.MEMBER_ROUTE_PREFIX + "/passengerInviteCampaign")
public class MbrPassengerInviteCampaignController extends BaseController {

    private final IMktPassengerInviteCampaignService mktPassengerInviteCampaignService;
}
