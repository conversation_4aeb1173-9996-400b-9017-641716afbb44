package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.market.CouponStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 乘客推乘客邀请记录详情视图对象（联查数据）
 *
 * <AUTHOR>
 * @date 2025-08-16
 */
@Data
@ExcelIgnoreUnannotated
public class MktPassengerInviteRecordDetailVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 邀请记录ID
     */
    @ExcelProperty(value = "邀请记录ID")
    private Long inviteRecordId;

    /**
     * 活动ID
     */
    @ExcelProperty(value = "活动ID")
    private Long campaignId;

    /**
     * 邀请人乘客ID
     */
    @ExcelProperty(value = "邀请人乘客ID")
    private Long inviterId;

    /**
     * 邀请人手机号
     */
    @ExcelProperty(value = "邀请人手机号")
    private String inviterMobile;

    /**
     * 邀请人城市
     */
    @ExcelProperty(value = "邀请人城市")
    private String inviterCityCode;

    /**
     * 被邀请人乘客ID
     */
    @ExcelProperty(value = "被邀请人乘客ID")
    private Long inviteeId;

    /**
     * 被邀请人手机号
     */
    @ExcelProperty(value = "被邀请人手机号")
    private String inviteeMobile;

    /**
     * 被邀请人城市
     */
    @ExcelProperty(value = "被邀请人城市")
    private String inviteeCityCode;

    /**
     * 注册时间（邀请时间）
     */
    @ExcelProperty(value = "注册时间")
    private Date inviteTime;

    /**
     * 首单完成时间
     */
    @ExcelProperty(value = "首单完成时间")
    private Date orderCompleteTime;

    /**
     * 邀请状态(1已注册,2已首单)
     */
    @ExcelProperty(value = "邀请状态")
    @Enum2Text(enumClass = PassengerInviteStatusEnum.class)
    private String inviteStatus;

    /**
     * 奖励记录ID
     */
    @ExcelProperty(value = "奖励记录ID")
    private Long rewardId;

    /**
     * 角色类型(1邀请人,2被邀请人)
     */
    @ExcelProperty(value = "角色类型")
    @Enum2Text(enumClass = PassengerInviteRoleTypeEnum.class)
    private String roleType;

    /**
     * 发放条件(1注册成功,2完成首单)
     */
    @ExcelProperty(value = "发放条件")
    @Enum2Text(enumClass = PassengerInviteConditionTypeEnum.class)
    private String conditionType;

    /**
     * 奖励类型：1=现金 2=积分 3=卡券
     */
    @ExcelProperty(value = "奖励类型")
    @Enum2Text(enumClass = PassengerInviteRewardTypeEnum.class)
    private String rewardType;

    /**
     * 奖励金额或积分
     */
    @ExcelProperty(value = "奖励金额")
    private BigDecimal rewardValue;

    /**
     * 奖励内容（格式化后的奖励描述）
     */
    @ExcelProperty(value = "奖励内容")
    private String rewardContent;

    /**
     * 奖励发放状态(1已发放,2发放失败,3客诉退券,4客诉退券失败)
     */
    @ExcelProperty(value = "奖励状态")
    @Enum2Text(enumClass = PassengerInviteRewardStatusEnum.class)
    private String rewardStatus;

    /**
     * 奖励时间（奖励记录创建时间）
     */
    @ExcelProperty(value = "奖励时间")
    private Date rewardTime;

    /**
     * 优惠券发放记录ID
     */
    @ExcelProperty(value = "优惠券发放记录ID")
    private Long couponGrantId;

    /**
     * 优惠券名称
     */
    @ExcelProperty(value = "优惠券名称")
    private String couponName;

    /**
     * 优惠券状态(0待使用,1已使用,2已过期,3已作废)
     */
    @ExcelProperty(value = "优惠券状态")
    @Enum2Text(enumClass = CouponStatusEnum.class)
    private String couponStatus;

    /**
     * 核销时间
     */
    @ExcelProperty(value = "核销时间")
    private Date wipedTime;

    /**
     * 核销订单号
     */
    @ExcelProperty(value = "核销订单号")
    private String orderNo;

    /**
     * 优惠券开始时间
     */
    @ExcelProperty(value = "优惠券开始时间")
    private Date couponStartTime;

    /**
     * 优惠券结束时间
     */
    @ExcelProperty(value = "优惠券结束时间")
    private Date couponEndTime;

}
