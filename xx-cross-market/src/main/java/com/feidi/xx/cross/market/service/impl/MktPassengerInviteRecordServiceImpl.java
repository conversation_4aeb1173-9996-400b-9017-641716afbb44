package com.feidi.xx.cross.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordBo;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordDetailQueryBo;
import com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRecordMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客邀请记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRecordServiceImpl implements IMktPassengerInviteRecordService {

    private final MktPassengerInviteRecordMapper baseMapper;

    /**
     * 查询乘客推乘客邀请记录
     *
     * @param id 主键
     * @return 乘客推乘客邀请记录
     */
    @Override
    public MktPassengerInviteRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客邀请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客邀请记录分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRecordVo> queryPageList(MktPassengerInviteRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客邀请记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客邀请记录列表
     */
    @Override
    public List<MktPassengerInviteRecordVo> queryList(MktPassengerInviteRecordBo bo) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteRecord> buildQueryWrapper(MktPassengerInviteRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteRecord::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getInviterId() != null, MktPassengerInviteRecord::getInviterId, bo.getInviterId());
        lqw.eq(StringUtils.isNotBlank(bo.getInviterMobile()), MktPassengerInviteRecord::getInviterMobile, bo.getInviterMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getInviterCityCode()), MktPassengerInviteRecord::getInviterCityCode, bo.getInviterCityCode());
        lqw.eq(bo.getInviteeId() != null, MktPassengerInviteRecord::getInviteeId, bo.getInviteeId());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteeMobile()), MktPassengerInviteRecord::getInviteeMobile, bo.getInviteeMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteeCityCode()), MktPassengerInviteRecord::getInviteeCityCode, bo.getInviteeCityCode());
        lqw.eq(bo.getInviteTime() != null, MktPassengerInviteRecord::getInviteTime, bo.getInviteTime());
        lqw.eq(bo.getOrderCompleteTime() != null, MktPassengerInviteRecord::getOrderCompleteTime, bo.getOrderCompleteTime());
        lqw.eq(bo.getOrderId() != null, MktPassengerInviteRecord::getOrderId, bo.getOrderId());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteRecord::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 根据活动id查询邀请统计
     *
     * @param campaignIds 活动id集合
     * @return 邀请统计列表
     */
    @Override
    public Map<Long, InviteStatisticsVo> queryInviteStatisticsByCampaignIds(Collection<Long> campaignIds) {
        List<InviteStatisticsVo> list = baseMapper.selectInviteStatisticsByCampaignIds(campaignIds);
        Map<Long, InviteStatisticsVo> map = new HashMap<>();
        for (Long campaignId : campaignIds) {
            //如果活动不存在的则返回0
            list.stream().filter(item -> item.getCampaignId().equals(campaignId)).findFirst()
                    .ifPresentOrElse(item -> map.put(campaignId, item), () -> map.put(campaignId, new InviteStatisticsVo(campaignId)));
        }
        return map;
    }

    /**
     * 分页查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 邀请记录详情分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRecordDetailVo> queryInviteRecordDetailPageList(MktPassengerInviteRecordDetailQueryBo bo, PageQuery pageQuery) {
        Page<MktPassengerInviteRecordDetailVo> page = baseMapper.selectInviteRecordDetailPage(pageQuery.build(), bo);
        return TableDataInfo.build(page);
    }

    /**
     * 查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param bo 查询条件
     * @return 邀请记录详情列表
     */
    @Override
    public List<MktPassengerInviteRecordDetailVo> queryInviteRecordDetailList(MktPassengerInviteRecordDetailQueryBo bo) {
        return baseMapper.selectInviteRecordDetailList(bo);
    }
}
