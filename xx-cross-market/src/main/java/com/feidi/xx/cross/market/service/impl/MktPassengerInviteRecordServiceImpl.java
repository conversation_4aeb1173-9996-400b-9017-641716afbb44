package com.feidi.xx.cross.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.core.utils.MapstructUtils;
import com.feidi.xx.common.core.utils.StringUtils;
import com.feidi.xx.common.mybatis.core.page.PageQuery;
import com.feidi.xx.common.mybatis.core.page.TableDataInfo;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordBo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import com.feidi.xx.cross.market.mapper.MktPassengerInviteRecordMapper;
import com.feidi.xx.cross.market.service.IMktPassengerInviteRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 乘客推乘客邀请记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@RequiredArgsConstructor
@Service
public class MktPassengerInviteRecordServiceImpl implements IMktPassengerInviteRecordService {

    private final MktPassengerInviteRecordMapper baseMapper;

    /**
     * 查询乘客推乘客邀请记录
     *
     * @param id 主键
     * @return 乘客推乘客邀请记录
     */
    @Override
    public MktPassengerInviteRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询乘客推乘客邀请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 乘客推乘客邀请记录分页列表
     */
    @Override
    public TableDataInfo<MktPassengerInviteRecordVo> queryPageList(MktPassengerInviteRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = buildQueryWrapper(bo);
        Page<MktPassengerInviteRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的乘客推乘客邀请记录列表
     *
     * @param bo 查询条件
     * @return 乘客推乘客邀请记录列表
     */
    @Override
    public List<MktPassengerInviteRecordVo> queryList(MktPassengerInviteRecordBo bo) {
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<MktPassengerInviteRecord> buildQueryWrapper(MktPassengerInviteRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<MktPassengerInviteRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCampaignId() != null, MktPassengerInviteRecord::getCampaignId, bo.getCampaignId());
        lqw.eq(bo.getInviterId() != null, MktPassengerInviteRecord::getInviterId, bo.getInviterId());
        lqw.eq(StringUtils.isNotBlank(bo.getInviterMobile()), MktPassengerInviteRecord::getInviterMobile, bo.getInviterMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getInviterCityCode()), MktPassengerInviteRecord::getInviterCityCode, bo.getInviterCityCode());
        lqw.eq(bo.getInviteeId() != null, MktPassengerInviteRecord::getInviteeId, bo.getInviteeId());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteeMobile()), MktPassengerInviteRecord::getInviteeMobile, bo.getInviteeMobile());
        lqw.eq(StringUtils.isNotBlank(bo.getInviteeCityCode()), MktPassengerInviteRecord::getInviteeCityCode, bo.getInviteeCityCode());
        lqw.eq(bo.getInviteTime() != null, MktPassengerInviteRecord::getInviteTime, bo.getInviteTime());
        lqw.eq(bo.getOrderCompleteTime() != null, MktPassengerInviteRecord::getOrderCompleteTime, bo.getOrderCompleteTime());
        lqw.eq(bo.getOrderId() != null, MktPassengerInviteRecord::getOrderId, bo.getOrderId());
        lqw.eq(bo.getStatus() != null, MktPassengerInviteRecord::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增乘客推乘客邀请记录
     *
     * @param bo 乘客推乘客邀请记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(MktPassengerInviteRecordBo bo) {
        MktPassengerInviteRecord add = MapstructUtils.convert(bo, MktPassengerInviteRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改乘客推乘客邀请记录
     *
     * @param bo 乘客推乘客邀请记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(MktPassengerInviteRecordBo bo) {
        MktPassengerInviteRecord update = MapstructUtils.convert(bo, MktPassengerInviteRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(MktPassengerInviteRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除乘客推乘客邀请记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
