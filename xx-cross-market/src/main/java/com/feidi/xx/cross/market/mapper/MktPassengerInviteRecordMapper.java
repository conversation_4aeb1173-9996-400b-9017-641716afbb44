package com.feidi.xx.cross.market.mapper;

import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 乘客推乘客邀请记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface MktPassengerInviteRecordMapper extends BaseMapperPlus<MktPassengerInviteRecord, MktPassengerInviteRecordVo> {

    /**
     * 根据活动id查询邀请统计
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum
     * @param campaignIds 活动id集合
     * @return 邀请统计列表
     */
    List<InviteStatisticsVo> selectInviteStatisticsByCampaignIds(@Param("campaignIds") Collection<Long> campaignIds);

    /**
     * 查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param campaignId 活动ID
     * @param inviterMobile 邀请人手机号
     * @param inviteeMobile 被邀请人手机号
     * @param inviteStatus 邀请状态
     * @param rewardStatus 奖励状态
     * @param couponStatus 优惠券状态
     * @return 邀请记录详情列表
     */
    List<MktPassengerInviteRecordDetailVo> selectInviteRecordDetailList(
            @Param("campaignId") Long campaignId,
            @Param("inviterMobile") String inviterMobile,
            @Param("inviteeMobile") String inviteeMobile,
            @Param("inviteStatus") String inviteStatus,
            @Param("rewardStatus") String rewardStatus,
            @Param("couponStatus") String couponStatus
    );

}
