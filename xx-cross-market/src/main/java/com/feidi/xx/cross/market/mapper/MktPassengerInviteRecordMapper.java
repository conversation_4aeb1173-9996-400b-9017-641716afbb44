package com.feidi.xx.cross.market.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import com.feidi.xx.cross.market.domain.bo.MktPassengerInviteRecordDetailQueryBo;
import com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 乘客推乘客邀请记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface MktPassengerInviteRecordMapper extends BaseMapperPlus<MktPassengerInviteRecord, MktPassengerInviteRecordVo> {

    /**
     * 根据活动id查询邀请统计
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum
     * @param campaignIds 活动id集合
     * @return 邀请统计列表
     */
    List<InviteStatisticsVo> selectInviteStatisticsByCampaignIds(@Param("campaignIds") Collection<Long> campaignIds);

    /**
     * 查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param bo 查询条件
     * @return 邀请记录详情列表
     */
    List<MktPassengerInviteRecordDetailVo> selectInviteRecordDetailList(@Param("bo") MktPassengerInviteRecordDetailQueryBo bo);

    /**
     * 分页查询乘客推乘客邀请记录详情列表（联查奖励和优惠券信息）
     *
     * @param page 分页参数
     * @param bo 查询条件
     * @return 邀请记录详情分页列表
     */
    Page<MktPassengerInviteRecordDetailVo> selectInviteRecordDetailPage(Page<MktPassengerInviteRecordDetailVo> page, @Param("bo") MktPassengerInviteRecordDetailQueryBo bo);

}
