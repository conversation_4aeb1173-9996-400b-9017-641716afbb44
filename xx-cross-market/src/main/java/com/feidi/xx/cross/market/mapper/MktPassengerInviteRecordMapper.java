package com.feidi.xx.cross.market.mapper;

import com.feidi.xx.common.mybatis.core.mapper.BaseMapperPlus;
import com.feidi.xx.cross.market.domain.MktPassengerInviteRecord;
import com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo;
import com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 乘客推乘客邀请记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
public interface MktPassengerInviteRecordMapper extends BaseMapperPlus<MktPassengerInviteRecord, MktPassengerInviteRecordVo> {

    /**
     * 根据活动id查询邀请统计
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum
     * @param campaignIds 活动id集合
     * @return 邀请统计列表
     */
    List<InviteStatisticsVo> selectInviteStatisticsByCampaignIds(@Param("campaignIds") Collection<Long> campaignIds);

}
