package com.feidi.xx.cross.market.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.feidi.xx.common.enum2text.annotation.Enum2Text;
import com.feidi.xx.cross.common.enums.market.PassengerInviteCampaignStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.market.domain.MktPassengerInviteCampaign;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;


/**
 * 乘客推乘客活动主视图对象 mkt_passenger_invite_campaign
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = MktPassengerInviteCampaign.class)
public class MktPassengerInviteCampaignVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    private Long id;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    private String activityName;

    /**
     * 活动开始时间
     */
    @ExcelProperty(value = "活动开始时间")
    private Date startTime;

    /**
     * 活动结束时间
     */
    @ExcelProperty(value = "活动结束时间")
    private Date endTime;

    /**
     * 活动状态 0:待开始 1:进行中 2:已结束
     */
    @ExcelProperty(value = "活动状态 0:待开始 1:进行中 2:已结束")
    @Enum2Text(enumClass = PassengerInviteCampaignStatusEnum.class)
    private String status;

    /**
     * 活动规则
     */
    @ExcelProperty(value = "活动规则")
    private String ruleContent;

    /**
     * 单个用户最多可邀请人数(0表示不限)
     */
    @ExcelProperty(value = "单个用户最多可邀请人数(0表示不限)")
    private Long maxInvites;

    /**
     * 分享标题
     */
    @ExcelProperty(value = "分享标题")
    private String shareTitle;

    /**
     * 首页文案
     */
    @ExcelProperty(value = "首页文案")
    private String entranceText;

    /**
     * 城市code数组
     */
    @ExcelProperty(value = "城市code数组")
    private List<String> cityCode;

    /**
     * 城市名称数组
     */
    @ExcelProperty(value = "城市名称数组")
    private List<String> cityNames;

    /**
     * 邀请注册人数
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum
     */
    @ExcelProperty(value = "邀请注册人数")
    private Long inviteRegisterCount = 0L;
    /**
     * 邀请完单人数
     *
     * @see com.feidi.xx.cross.common.enums.market.PassengerInviteStatusEnum
     */
    @ExcelProperty(value = "邀请完单人数")
    private Long inviteCompleteOrderCount = 0L;

    /**
     * 奖励配置列表
     */
    private List<MktPassengerInviteRewardConfigVo> rewardConfigList = new ArrayList<>();

    public void setRewardConfigList(List<MktPassengerInviteRewardConfigVo> rewardConfigList) {
        this.rewardConfigList = rewardConfigList;
        if (rewardConfigList != null) {
            this.couponStock = rewardConfigList.stream()
                    .filter(e -> e.getRewardType().equals(PassengerInviteRewardTypeEnum.COUPON.getCode()))
                    .map(MktPassengerInviteRewardConfigVo::getCouponVos)
                    .flatMap(Collection::stream)
                    .mapToInt(MktCouponVo::getMargin).sum();
        }
    }

    /**
     * 优惠券余量
     */
    private Integer couponStock = 0;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    public void setInviteStatistics(InviteStatisticsVo inviteStatisticsVo) {
        if (inviteStatisticsVo != null) {
            this.inviteRegisterCount = inviteStatisticsVo.getInvitedCount();
            this.inviteCompleteOrderCount = inviteStatisticsVo.getCompletedCount();
        }
    }
}