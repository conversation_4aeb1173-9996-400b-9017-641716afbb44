package com.feidi.xx.cross.market.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.feidi.xx.common.tenant.core.TenantEntity;
import com.feidi.xx.cross.common.enums.market.PassengerInviteConditionTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardStatusEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRewardTypeEnum;
import com.feidi.xx.cross.common.enums.market.PassengerInviteRoleTypeEnum;
import com.feidi.xx.cross.market.domain.common.RewardMeta;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.math.BigDecimal;

/**
 * 乘客推乘客奖励发放记录对象 mkt_passenger_invite_reward
 *
 * <AUTHOR>
 * @date 2025-08-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "mkt_passenger_invite_reward", autoResultMap = true)
public class MktPassengerInviteReward extends TenantEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 奖励记录ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 活动ID
     */
    private Long campaignId;

    /**
     * 对应邀请记录ID
     */
    private Long inviteRecordId;

    /**
     * 奖励配置ID
     */
    private Long rewardConfigId;

    /**
     * 奖励接收人乘客ID
     */
    private Long passengerId;

    /**
     * 角色类型(1邀请人,2被邀请人)
     *
     * @see PassengerInviteRoleTypeEnum
     */
    private String roleType;

    /**
     * 发放条件(1注册成功,2完成首单)
     *
     * @see PassengerInviteConditionTypeEnum
     */
    private String conditionType;

    /**
     * 1=现金 2=积分 3=卡券
     *
     * @see PassengerInviteRewardTypeEnum
     */
    private String rewardType;

    /**
     * 奖励金额或积分
     */
    private BigDecimal rewardValue;

    /**
     * 扩展字段(如发放的具体券ID数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private RewardMeta rewardMeta;

    /**
     * 优惠券发放表id
     */
    private Long couponGrantId;

    /**
     * 发放状态(1已发放,2发放失败,3客诉退券,4,客诉退券失败)
     *
     * @see PassengerInviteRewardStatusEnum
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


}
