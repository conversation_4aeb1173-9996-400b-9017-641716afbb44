<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.market.mapper.MktPassengerInviteRecordMapper">

    <select id="selectInviteStatisticsByCampaignIds" resultType="com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo">
        SELECT
        campaign_id as campaignId,
        COUNT(*) AS invitedCount,
        COUNT(CASE WHEN status = '2' THEN 1 END) AS completedCount
        FROM
        mkt_passenger_invite_record
        WHERE
        campaign_id IN
        <foreach item="item" index="index" collection="campaignIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        campaign_id
    </select>

    <select id="selectInviteRecordDetailList" resultType="com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo">
        SELECT
            mir.id AS inviteRecordId,
            mir.campaign_id AS campaignId,
            mir.inviter_id AS inviterId,
            mir.inviter_mobile AS inviterMobile,
            mir.inviter_city_code AS inviterCityCode,
            mir.invitee_id AS inviteeId,
            mir.invitee_mobile AS inviteeMobile,
            mir.invitee_city_code AS inviteeCityCode,
            mir.invite_time AS inviteTime,
            mir.order_complete_time AS orderCompleteTime,
            mir.status AS inviteStatus,

            mpr.id AS rewardId,
            mpr.role_type AS roleType,
            mpr.condition_type AS conditionType,
            mpr.reward_type AS rewardType,
            mpr.reward_value AS rewardValue,
            CASE
                WHEN mpr.reward_type = '1' THEN CONCAT(IFNULL(mpr.reward_value, 0), '元现金')
                WHEN mpr.reward_type = '2' THEN CONCAT(IFNULL(mpr.reward_value, 0), '积分')
                WHEN mpr.reward_type = '3' THEN IFNULL(mcg.coupon_name, '优惠券')
                ELSE '未知奖励'
            END AS rewardContent,
            mpr.status AS rewardStatus,
            mpr.create_time AS rewardTime,

            mcg.id AS couponGrantId,
            mcg.coupon_name AS couponName,
            mcg.using_status AS couponStatus,
            mcg.wiped_time AS wipedTime,
            mcg.order_no AS orderNo,
            mcg.start_time AS couponStartTime,
            mcg.end_time AS couponEndTime
        FROM
            mkt_passenger_invite_record mir
        LEFT JOIN
            mkt_passenger_invite_reward mpr ON mir.id = mpr.invite_record_id AND mpr.del_flag = '0'
        LEFT JOIN
            mkt_coupon_grant mcg ON mpr.coupon_grant_id = mcg.id AND mcg.del_flag = '0'
        WHERE
            mir.del_flag = '0'
            <if test="bo.campaignId != null">
                AND mir.campaign_id = #{bo.campaignId}
            </if>
            <if test="bo.inviterMobile != null and bo.inviterMobile != ''">
                AND mir.inviter_mobile LIKE CONCAT('%', #{bo.inviterMobile}, '%')
            </if>
            <if test="bo.inviteeMobile != null and bo.inviteeMobile != ''">
                AND mir.invitee_mobile LIKE CONCAT('%', #{bo.inviteeMobile}, '%')
            </if>
            <if test="bo.inviteStatus != null and bo.inviteStatus != ''">
                AND mir.status = #{bo.inviteStatus}
            </if>
            <if test="bo.rewardStatus != null and bo.rewardStatus != ''">
                AND mpr.status = #{bo.rewardStatus}
            </if>
            <if test="bo.couponStatus != null and bo.couponStatus != ''">
                AND mcg.using_status = #{bo.couponStatus}
            </if>
        ORDER BY
            mir.create_time DESC, mpr.create_time DESC
    </select>

    <select id="selectInviteRecordDetailPage" resultType="com.feidi.xx.cross.market.domain.vo.MktPassengerInviteRecordDetailVo">
        SELECT
            mir.id AS inviteRecordId,
            mir.campaign_id AS campaignId,
            mir.inviter_id AS inviterId,
            mir.inviter_mobile AS inviterMobile,
            mir.inviter_city_code AS inviterCityCode,
            mir.invitee_id AS inviteeId,
            mir.invitee_mobile AS inviteeMobile,
            mir.invitee_city_code AS inviteeCityCode,
            mir.invite_time AS inviteTime,
            mir.order_complete_time AS orderCompleteTime,
            mir.status AS inviteStatus,

            mpr.id AS rewardId,
            mpr.role_type AS roleType,
            mpr.condition_type AS conditionType,
            mpr.reward_type AS rewardType,
            mpr.reward_value AS rewardValue,
            CASE
                WHEN mpr.reward_type = '1' THEN CONCAT(IFNULL(mpr.reward_value, 0), '元现金')
                WHEN mpr.reward_type = '2' THEN CONCAT(IFNULL(mpr.reward_value, 0), '积分')
                WHEN mpr.reward_type = '3' THEN IFNULL(mcg.coupon_name, '优惠券')
                ELSE '未知奖励'
            END AS rewardContent,
            mpr.status AS rewardStatus,
            mpr.create_time AS rewardTime,

            mcg.id AS couponGrantId,
            mcg.coupon_name AS couponName,
            mcg.using_status AS couponStatus,
            mcg.wiped_time AS wipedTime,
            mcg.order_no AS orderNo,
            mcg.start_time AS couponStartTime,
            mcg.end_time AS couponEndTime
        FROM
            mkt_passenger_invite_record mir
        LEFT JOIN
            mkt_passenger_invite_reward mpr ON mir.id = mpr.invite_record_id AND mpr.del_flag = '0'
        LEFT JOIN
            mkt_coupon_grant mcg ON mpr.coupon_grant_id = mcg.id AND mcg.del_flag = '0'
        WHERE
            mir.del_flag = '0'
            <if test="bo.campaignId != null">
                AND mir.campaign_id = #{bo.campaignId}
            </if>
            <if test="bo.inviterMobile != null and bo.inviterMobile != ''">
                AND mir.inviter_mobile LIKE CONCAT('%', #{bo.inviterMobile}, '%')
            </if>
            <if test="bo.inviteeMobile != null and bo.inviteeMobile != ''">
                AND mir.invitee_mobile LIKE CONCAT('%', #{bo.inviteeMobile}, '%')
            </if>
            <if test="bo.inviteStatus != null and bo.inviteStatus != ''">
                AND mir.status = #{bo.inviteStatus}
            </if>
            <if test="bo.rewardStatus != null and bo.rewardStatus != ''">
                AND mpr.status = #{bo.rewardStatus}
            </if>
            <if test="bo.couponStatus != null and bo.couponStatus != ''">
                AND mcg.using_status = #{bo.couponStatus}
            </if>
        ORDER BY
            mir.create_time DESC, mpr.create_time DESC
    </select>
</mapper>
