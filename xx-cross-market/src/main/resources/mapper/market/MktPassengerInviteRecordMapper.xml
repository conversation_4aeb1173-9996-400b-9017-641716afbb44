<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.feidi.xx.cross.market.mapper.MktPassengerInviteRecordMapper">

    <select id="selectInviteStatisticsByCampaignIds" resultType="com.feidi.xx.cross.market.domain.vo.InviteStatisticsVo">
        SELECT
        campaign_id as campaignId,
        COUNT(*) AS invitedCount,
        COUNT(CASE WHEN status = '2' THEN 1 END) AS completedCount
        FROM
        mkt_passenger_invite_record
        WHERE
        campaign_id IN
        <foreach item="item" index="index" collection="campaignIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        campaign_id
    </select>
</mapper>
