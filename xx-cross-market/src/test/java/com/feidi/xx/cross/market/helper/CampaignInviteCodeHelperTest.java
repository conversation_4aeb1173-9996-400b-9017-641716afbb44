package com.feidi.xx.cross.market.helper;

import com.feidi.xx.common.tenant.helper.TenantHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class CampaignInviteCodeHelperTest {

    @Autowired
    private CampaignInviteCodeHelper campaignInviteCodeHelper;

    private static final Long ACTIVITY_ID = 1903342790375727106L;
    private static final Long USER_ID = 1904459727978733569L;
    private static final Long USER_ID_2 = 2002L;

    @BeforeEach
    void setUp() {
        // 清理测试数据
        cleanupTestData();
        TenantHelper.setDynamic("test1001");
    }

    @AfterEach
    void tearDown() {
        // 清理测试数据
        cleanupTestData();
    }

    private void cleanupTestData() {
        try {
            campaignInviteCodeHelper.deleteAllInviteCodesByActivity(ACTIVITY_ID);
        } catch (Exception e) {
            // 忽略清理异常
        }
    }

    @Test
    void testCreateAndSaveInviteCode_NewUser_Success() {
        // When
        String result = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // Then
        assertNotNull(result);
        assertEquals(8, result.length()); // 默认长度为8
        assertTrue(result.matches("[ABCDEFGHJKLMNPQRSTUVWXYZ23456789]{8}")); // 验证字符集

        // 验证邀请码已保存
        Long savedUserId = campaignInviteCodeHelper.getUserIdByInviteCode(ACTIVITY_ID, result);
        assertEquals(USER_ID, savedUserId);

        // 验证用户映射已保存
        String savedCode = campaignInviteCodeHelper.getInviteCodeByUser(ACTIVITY_ID, USER_ID);
        assertEquals(result, savedCode);

        // 验证邀请码存在性检查
        assertTrue(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, result));
        assertTrue(campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID));
    }

    @Test
    void testCreateAndSaveInviteCode_ExistingUser_ReturnExistingCode() {
        // Given - 先创建一个邀请码
        String firstCode = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // When - 再次为同一用户创建邀请码
        String secondCode = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // Then - 应该返回相同的邀请码
        assertEquals(firstCode, secondCode);
    }

    @Test
    void testGetUserIdByInviteCode_ValidCode_ReturnUserId() {
        // Given - 先创建一个邀请码
        String inviteCode = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // When
        Long result = campaignInviteCodeHelper.getUserIdByInviteCode(ACTIVITY_ID, inviteCode);

        // Then
        assertEquals(USER_ID, result);
    }

    @Test
    void testGetUserIdByInviteCode_InvalidCode_ReturnNull() {
        // When
        Long result = campaignInviteCodeHelper.getUserIdByInviteCode(ACTIVITY_ID, "INVALID123");

        // Then
        assertNull(result);
    }

    @Test
    void testGetInviteCodeByUser_ValidUser_ReturnCode() {
        // Given - 先创建一个邀请码
        String expectedCode = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // When
        String result = campaignInviteCodeHelper.getInviteCodeByUser(ACTIVITY_ID, USER_ID);

        // Then
        assertEquals(expectedCode, result);
    }

    @Test
    void testGetInviteCodeByUser_InvalidUser_ReturnNull() {
        // When
        String result = campaignInviteCodeHelper.getInviteCodeByUser(ACTIVITY_ID, 9999L);

        // Then
        assertNull(result);
    }

    @Test
    void testDeleteInviteCode_ValidCode_Success() {
        // Given - 先创建一个邀请码
        String inviteCode = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // 验证邀请码存在
        assertTrue(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, inviteCode));
        assertTrue(campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID));

        // When
        campaignInviteCodeHelper.deleteInviteCode(ACTIVITY_ID, inviteCode);

        // Then - 验证邀请码已删除
        assertFalse(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, inviteCode));
        assertFalse(campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID));
        assertNull(campaignInviteCodeHelper.getUserIdByInviteCode(ACTIVITY_ID, inviteCode));
        assertNull(campaignInviteCodeHelper.getInviteCodeByUser(ACTIVITY_ID, USER_ID));
    }

    @Test
    void testDeleteInviteCode_InvalidCode_NoOperation() {
        // When - 删除不存在的邀请码
        campaignInviteCodeHelper.deleteInviteCode(ACTIVITY_ID, "INVALID123");

        // Then - 不应该抛出异常，操作应该安全完成
        // 这里主要测试方法不会因为无效邀请码而抛出异常
    }

    @Test
    void testDeleteUserInviteCode_ValidUser_Success() {
        // Given - 先创建一个邀请码
        String inviteCode = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // 验证邀请码存在
        assertTrue(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, inviteCode));
        assertTrue(campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID));

        // When
        campaignInviteCodeHelper.deleteUserInviteCode(ACTIVITY_ID, USER_ID);

        // Then - 验证邀请码已删除
        assertFalse(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, inviteCode));
        assertFalse(campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID));
        assertNull(campaignInviteCodeHelper.getUserIdByInviteCode(ACTIVITY_ID, inviteCode));
        assertNull(campaignInviteCodeHelper.getInviteCodeByUser(ACTIVITY_ID, USER_ID));
    }

    @Test
    void testDeleteAllInviteCodesByActivity_Success() {
        // Given - 创建多个邀请码
        String code1 = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);
        String code2 = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID_2);

        // 验证邀请码存在
        assertTrue(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, code1));
        assertTrue(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, code2));
        assertEquals(2, campaignInviteCodeHelper.getInviteCodeCount(ACTIVITY_ID));

        // When
        int result = campaignInviteCodeHelper.deleteAllInviteCodesByActivity(ACTIVITY_ID);

        // Then
        assertTrue(result > 0); // 应该删除了一些key
        assertEquals(0, campaignInviteCodeHelper.getInviteCodeCount(ACTIVITY_ID));
        assertFalse(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, code1));
        assertFalse(campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, code2));
    }

    @Test
    void testDeleteAllInviteCodesByActivities_Success() {
        // Given
        Long activityId1 = 1001L;
        Long activityId2 = 1002L;
        List<Long> activityIds = Arrays.asList(activityId1, activityId2);

        // 为不同活动创建邀请码
        campaignInviteCodeHelper.createAndSaveInviteCode(activityId1, USER_ID);
        campaignInviteCodeHelper.createAndSaveInviteCode(activityId2, USER_ID);

        // When
        int result = campaignInviteCodeHelper.deleteAllInviteCodesByActivities(activityIds);

        // Then
        assertTrue(result > 0); // 应该删除了一些key
        assertEquals(0, campaignInviteCodeHelper.getInviteCodeCount(activityId1));
        assertEquals(0, campaignInviteCodeHelper.getInviteCodeCount(activityId2));
    }

    @Test
    void testExistsInviteCode_CodeExists_ReturnTrue() {
        // Given - 先创建一个邀请码
        String inviteCode = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // When
        boolean result = campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, inviteCode);

        // Then
        assertTrue(result);
    }

    @Test
    void testExistsInviteCode_CodeNotExists_ReturnFalse() {
        // When
        boolean result = campaignInviteCodeHelper.existsInviteCode(ACTIVITY_ID, "NOTEXIST");

        // Then
        assertFalse(result);
    }

    @Test
    void testExistsUserInviteCode_UserExists_ReturnTrue() {
        // Given - 先创建一个邀请码
        campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // When
        boolean result = campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, USER_ID);

        // Then
        assertTrue(result);
    }

    @Test
    void testExistsUserInviteCode_UserNotExists_ReturnFalse() {
        // When
        boolean result = campaignInviteCodeHelper.existsUserInviteCode(ACTIVITY_ID, 9999L);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetInviteCodeCount_ReturnCorrectCount() {
        // Given - 创建多个邀请码
        campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);
        campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID_2);

        // When
        long result = campaignInviteCodeHelper.getInviteCodeCount(ACTIVITY_ID);

        // Then
        assertEquals(2, result);
    }

    @Test
    void testCreateAndSaveInviteCode_UserAlreadyHasCode_ThrowException() {
        // Given - 先创建一个邀请码
        campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);

        // 手动清除用户映射但保留邀请码映射，模拟数据不一致的情况
        // 这种情况下，当尝试保存时应该检测到用户已有邀请码

        // When & Then - 再次创建应该返回已存在的邀请码，而不是抛出异常
        String result = campaignInviteCodeHelper.createAndSaveInviteCode(ACTIVITY_ID, USER_ID);
        assertNotNull(result);
    }

    @Test
    void testMultipleActivities_IsolatedData() {
        // Given
        Long activityId1 = 1001L;
        Long activityId2 = 1002L;

        // When - 为不同活动创建邀请码
        String code1 = campaignInviteCodeHelper.createAndSaveInviteCode(activityId1, USER_ID);
        String code2 = campaignInviteCodeHelper.createAndSaveInviteCode(activityId2, USER_ID);

        // Then - 验证数据隔离
        assertNotEquals(code1, code2);
        assertEquals(USER_ID, campaignInviteCodeHelper.getUserIdByInviteCode(activityId1, code1));
        assertEquals(USER_ID, campaignInviteCodeHelper.getUserIdByInviteCode(activityId2, code2));
        assertNull(campaignInviteCodeHelper.getUserIdByInviteCode(activityId1, code2));
        assertNull(campaignInviteCodeHelper.getUserIdByInviteCode(activityId2, code1));

        // 清理测试数据
        campaignInviteCodeHelper.deleteAllInviteCodesByActivity(activityId2);
    }
}
